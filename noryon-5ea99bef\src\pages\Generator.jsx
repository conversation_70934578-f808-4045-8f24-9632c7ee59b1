
import React, { useState, useEffect } from "react";
import { GeneratedApp } from "@/api/entities";
import { User } from "@/api/entities";
import { generateWebAppCode } from "@/api/webCodeGeneration";
import { motion, AnimatePresence } from "framer-motion";
import { Sparkles, Zap, Star, Eye, Heart, MessageCircle, TrendingUp, X, ChevronRight, Search, Filter, Code } from "lucide-react";
import { useNavigate, Link } from "react-router-dom";
import { createPageUrl } from "@/utils";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";

import AppIdeaForm from "../components/generator/AppIdeaForm";
import AppCard from "../components/apps/AppCard";

export default function Generator() {
  const navigate = useNavigate();
  const [isGenerating, setIsGenerating] = useState(false);
  const [isSaving, setIsSaving] = useState(false); // This state is defined but not used in the provided outline. Preserving it.
  const [recentApps, setRecentApps] = useState([]);
  const [user, setUser] = useState(null);
  const [error, setError] = useState('');
  const [generationProgress, setGenerationProgress] = useState(0);

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      const [fetchedApps, currentUser] = await Promise.all([
        GeneratedApp.list('-created_date', 8),
        User.me().catch(() => null)
      ]);
      setRecentApps(fetchedApps);
      setUser(currentUser);
    } catch (error) {
      console.error("Error loading data:", error);
      setError('Failed to load your projects. Please refresh the page.');
    }
  }

  const generateApp = async (formData) => {
    if (!formData.description?.trim()) {
      setError('Please describe your app idea before generating.');
      return;
    }

    setIsGenerating(true);
    setError('');
    setGenerationProgress(0);
    
    let progressInterval;

    try {
      // Simulate progress updates
      progressInterval = setInterval(() => {
        setGenerationProgress(prev => {
          if (prev >= 90) return prev;
          return Math.min(prev + Math.random() * 15, 90); // Cap at 90 to show final step
        });
      }, 300);

      const isAuthenticated = Boolean(user);
      
      // Enhanced prompt with template information
      let prompt = `Generate a mobile app concept for: "${formData.description}"`;
      
      // Add template context if selected
      if (formData.selectedTemplate) {
        const template = formData.selectedTemplate;
        prompt += `

DESIGN TEMPLATE SELECTED: ${template.name}
Template Style: ${template.description}
Design Characteristics: ${template.characteristics?.join(', ') || template.tags?.join(', ')}
Color Palette: ${template.colors?.join(', ')}
Best For: ${template.bestFor?.join(', ')}

Please ensure the generated app concept incorporates this design template's style and characteristics.`;
      }
      
      prompt += `
        
        Return JSON with:
        - app_name: Catchy 2-3 word name
        - overview: 2 sentence description
        - key_features: Array of 5 key features
        - target_audience: Brief description
        - technology_stack: Array of 5 React Native technologies
        - monetization: Single revenue strategy description
        - development_timeline: "MVP in X weeks, Launch in Y months"
        - unique_selling_points: Array of 3 differentiators
        - complexity_level: "simple", "moderate", "complex", or "enterprise"
        - market_size: Brief market analysis
        - risk_factors: Main challenge and solution`;

      // Add design_template field if template was selected
      const schemaProperties = {
        app_name: { type: "string" },
        overview: { type: "string" },
        key_features: { type: "array", items: { type: "string" }},
        target_audience: { type: "string" },
        technology_stack: { type: "array", items: { type: "string" }},
        monetization: { type: "string" },
        development_timeline: { type: "string" },
        unique_selling_points: { type: "array", items: { type: "string" }},
        complexity_level: { type: "string" },
        market_size: { type: "string" },
        risk_factors: { type: "string" }
      };

      // Add design template info to schema if template selected
      if (formData.selectedTemplate) {
        schemaProperties.design_template = {
          type: "object",
          properties: {
            name: { type: "string" },
            style_applied: { type: "string" },
            color_scheme: { type: "string" }
          }
        };
      }

      setGenerationProgress(30);

      // Use backend-powered generator for web app code
      const webFileTree = await generateWebAppCode(prompt);
      localStorage.setItem("latest_web_file_tree", JSON.stringify(webFileTree));

      const response = { app_name: formData.app_name || "Web Project", overview: formData.description, key_features: [], target_audience: "web" };

      if (progressInterval) clearInterval(progressInterval);
      setGenerationProgress(70);

      // Validate and clean response
      if (!response?.app_name || !response?.overview) {
        throw new Error('Invalid AI response. Please try again.');
      }

      // Ensure all fields are properly formatted and provide default values
      const cleanedResponse = {
        app_name: response.app_name ? String(response.app_name).trim() : 'Unnamed App',
        overview: response.overview ? String(response.overview).trim() : 'A new mobile application.',
        key_features: Array.isArray(response.key_features) && response.key_features.length > 0 ? response.key_features : ['Core functionality', 'User authentication'],
        target_audience: response.target_audience ? String(response.target_audience) : 'General mobile users',
        technology_stack: Array.isArray(response.technology_stack) && response.technology_stack.length > 0 ? response.technology_stack : ['React Native', 'JavaScript', 'Firebase'],
        monetization: response.monetization ? String(response.monetization) : 'Freemium model with premium features',
        development_timeline: response.development_timeline ? String(response.development_timeline) : 'MVP in 8 weeks, Launch in 4 months',
        unique_selling_points: Array.isArray(response.unique_selling_points) && response.unique_selling_points.length > 0 ? response.unique_selling_points : ['User-friendly design', 'Innovative approach'],
        complexity_level: response.complexity_level || "moderate",
        market_size: response.market_size ? String(response.market_size) : 'Growing mobile market segment',
        risk_factors: response.risk_factors ? String(response.risk_factors) : 'Market competition - mitigated by unique features',
        design_template: response.design_template || (formData.selectedTemplate ? {
          name: formData.selectedTemplate.name,
          style_applied: formData.selectedTemplate.description,
          color_scheme: formData.selectedTemplate.colors?.join(', ') || 'Default colors'
        } : null)
      };

      setGenerationProgress(85);

      // Quick category detection
      const categoryKeywords = {
        social: ['social', 'chat', 'connect', 'share', 'community'],
        ecommerce: ['shop', 'buy', 'sell', 'store', 'marketplace'],
        health: ['health', 'fitness', 'medical', 'wellness'],
        education: ['learn', 'study', 'course', 'education'],
        entertainment: ['game', 'fun', 'entertainment', 'music'],
        finance: ['money', 'finance', 'budget', 'invest'],
        productivity: ['task', 'manage', 'organize', 'workflow'],
        utility: ['tool', 'utility', 'calculator', 'converter']
      };

      let detectedCategory = 'other';
      const searchText = formData.description.toLowerCase();
      
      for (const [category, keywords] of Object.entries(categoryKeywords)) {
        if (keywords.some(keyword => searchText.includes(keyword))) {
          detectedCategory = category;
          break;
        }
      }

      const appData = {
        name: cleanedResponse.app_name, // For backward compatibility with existing schema
        app_name: cleanedResponse.app_name,
        description: formData.description.trim(),
        generated_concept: cleanedResponse,
        category: detectedCategory,
        complexity_level: cleanedResponse.complexity_level,
        is_favorite: false,
        template_id: formData.selectedTemplate?.id || null,
        selected_template: formData.selectedTemplate ? {
          id: formData.selectedTemplate.id,
          name: formData.selectedTemplate.name,
          category: formData.selectedTemplate.category
        } : {},
        generation_metadata: {
          generated_at: new Date().toISOString(),
          user_authenticated: isAuthenticated,
          model_version: "v2.1-fast",
          template_used: formData.selectedTemplate?.id || null,
          generation_time: Date.now() // Timestamp of generation completion
        }
      };

      setGenerationProgress(95);

      const savedApp = await GeneratedApp.create(appData);
      
      setGenerationProgress(100);
      
      // Update local state immediately
      setRecentApps(prev => [savedApp, ...prev.slice(0, 7)]);
      
      // Small delay for smooth UX, then navigate
      setTimeout(() => {
        navigate(`${createPageUrl("AppPreview")}?id=${savedApp.id}`);
      }, 200);
      
    } catch (error) {
      console.error("Generation error:", error);
      setError(
        error.message?.includes('422') ? 'Invalid app description. Please provide more details about your app idea.' :
        error.message?.includes('network') ? 'Network error. Please check your connection and try again.' :
        error.message || 'Failed to generate app. Please try again.'
      );
    } finally {
      if (progressInterval) clearInterval(progressInterval);
      setIsGenerating(false);
      setGenerationProgress(0);
    }
  };

  const viewApp = (app) => {
    navigate(`${createPageUrl("AppPreview")}?id=${app.id}`);
  };

  const deleteApp = async (appId, event) => {
    event?.stopPropagation();
    if (!window.confirm('Are you sure you want to delete this app? This action cannot be undone.')) {
      return;
    }

    try {
      await GeneratedApp.delete(appId);
      setRecentApps(prev => prev.filter(app => app.id !== appId));
    } catch (error) {
      console.error("Error deleting app:", error);
      setError('Failed to delete the app. Please try again.');
    }
  };

  const toggleFavorite = async (app, event) => {
    event?.stopPropagation();
    
    try {
      setRecentApps(prev => 
        prev.map(a => a.id === app.id ? { ...a, is_favorite: !a.is_favorite } : a)
      );
      await GeneratedApp.update(app.id, {
        is_favorite: !app.is_favorite
      });
    } catch (error) {
      console.error("Error updating favorite:", error);
      setError('Failed to update favorite status. Please try again.');
      setRecentApps(prev => 
        prev.map(a => a.id === app.id ? { ...a, is_favorite: app.is_favorite } : a)
      );
    }
  };

  // Mock community projects data
  const communityProjects = [
    {
      id: 1,
      name: "pulse-robot-template",
      description: "Modern pulse robot template with dynamic animations",
      author: "Alex Chen",
      avatar: "AC",
      category: "Website",
      likes: 2050,
      views: 8540,
      preview_bg: "from-orange-400 to-red-500"
    },
    {
      id: 2,
      name: "cryptocurrency-trading-dash",
      description: "Real-time cryptocurrency trading dashboard with charts",
      author: "Sarah Kim",
      avatar: "SK", 
      category: "Website",
      likes: 1270,
      views: 5430,
      preview_bg: "from-gray-700 to-gray-900"
    },
    {
      id: 3,
      name: "wrkds-ai-integration", 
      description: "AI integration workflow for modern development",
      author: "Mike Rodriguez",
      avatar: "MR",
      category: "Website", 
      likes: 770,
      views: 3210,
      preview_bg: "from-blue-500 to-purple-600"
    },
    {
      id: 4,
      name: "crypto-trade-template",
      description: "Professional crypto trading interface template",
      author: "Lisa Park",
      avatar: "LP",
      category: "Website",
      likes: 650,
      views: 2890,
      preview_bg: "from-green-400 to-emerald-600"
    }
  ];

  // Enhanced animation variants
  const containerVariants = {
    initial: { opacity: 0 },
    animate: { 
      opacity: 1,
      transition: { 
        duration: 0.6,
        staggerChildren: 0.1,
        ease: [0.23, 1, 0.32, 1]
      }
    }
  };

  const itemVariants = {
    initial: { opacity: 0, y: 30, scale: 0.95 },
    animate: { 
      opacity: 1, 
      y: 0, 
      scale: 1,
      transition: { 
        duration: 0.5,
        ease: [0.23, 1, 0.32, 1]
      }
    }
  };

  const generatingVariants = {
    initial: { opacity: 0, scale: 0.9 },
    animate: { 
      opacity: 1, 
      scale: 1,
      transition: { 
        duration: 0.4,
        ease: [0.23, 1, 0.32, 1]
      }
    },
    exit: { 
      opacity: 0, 
      scale: 0.9,
      transition: { 
        duration: 0.3,
        ease: [0.76, 0, 0.24, 1]
      }
    }
  };

  return (
    <motion.div 
      className="min-h-screen p-4 md:p-8"
      variants={containerVariants}
      initial="initial"
      animate="animate"
    >
      <div className="w-full max-w-7xl mx-auto">
        {/* Error Message */}
        <AnimatePresence>
          {error && (
            <motion.div
              initial={{ opacity: 0, y: -20, scale: 0.95 }}
              animate={{ opacity: 1, y: 0, scale: 1 }}
              exit={{ opacity: 0, y: -20, scale: 0.95 }}
              transition={{ duration: 0.3, ease: [0.23, 1, 0.32, 1] }}
              className="mb-4 p-4 bg-gradient-to-r from-red-900/20 to-red-800/20 border border-red-500/50 rounded-xl backdrop-blur-sm"
            >
              <div className="flex items-start gap-3">
                <div className="w-5 h-5 bg-red-500 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                  <span className="text-white text-xs font-bold">!</span>
                </div>
                <div className="flex-1">
                  <p className="text-red-400 text-sm">{error}</p>
                  <motion.button 
                    onClick={() => setError('')}
                    className="text-red-400 hover:text-red-300 text-xs mt-2 underline transition-colors"
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    Dismiss
                  </motion.button>
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>

        <AnimatePresence mode="wait">
          {!isGenerating && (
            <motion.div
              key="app-form-section"
              variants={itemVariants}
              initial="initial"
              animate="animate"
              exit={{ opacity: 0, y: -30, transition: { duration: 0.3 } }}
            >
              <motion.div 
                className="text-center mb-8"
                variants={itemVariants}
              >
                <motion.h1 
                  className="text-4xl md:text-5xl lg:text-6xl font-bold bg-gradient-to-r from-white via-white to-gray-300 bg-clip-text text-transparent mb-4 tracking-tight"
                  variants={itemVariants}
                >
                  Build native mobile apps, fast.
                </motion.h1>
                <motion.p
                  className="text-lg md:text-xl text-gray-400 max-w-2xl mx-auto leading-relaxed mb-6"
                  variants={itemVariants}
                >
                  NORYON AI builds complete, cross-platform mobile apps using AI and React Native.
                </motion.p>

                {/* IDE Access Button */}
                <motion.div
                  className="flex justify-center mb-8"
                  variants={itemVariants}
                >
                  <Button
                    onClick={() => navigate(createPageUrl("CodeIDE") + "?sample=true")}
                    className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-6 py-3 rounded-xl font-medium transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl flex items-center gap-2"
                  >
                    <Code className="w-5 h-5" />
                    Open AI Code IDE
                    <ChevronRight className="w-4 h-4" />
                  </Button>
                </motion.div>
              </motion.div>
              
              <motion.div variants={itemVariants}>
                <AppIdeaForm 
                  onGenerate={generateApp}
                  isGenerating={isGenerating}
                />
              </motion.div>
            </motion.div>
          )}

          {isGenerating && (
            <motion.div 
              key="generating-state"
              className="text-center py-16"
              variants={generatingVariants}
              initial="initial"
              animate="animate"
              exit="exit"
            >
              {/* Improved Progress Indicator */}
              <motion.div 
                className="relative mx-auto mb-6"
                style={{ width: '80px', height: '80px' }}
              >
                {/* Background circle */}
                <div className="absolute inset-0 border-4 border-gray-800 rounded-full"></div>
                
                {/* Progress circle */}
                <motion.div
                  className="absolute inset-0 border-4 border-transparent border-t-blue-500 border-r-blue-500 rounded-full"
                  animate={{ rotate: 360 }}
                  transition={{ 
                    duration: 2, 
                    repeat: Infinity, 
                    ease: "linear" 
                  }}
                  style={{
                    clipPath: `polygon(50% 50%, 50% 0%, ${50 + Math.cos(2 * Math.PI * generationProgress / 100 - Math.PI/2) * 50}% ${50 + Math.sin(2 * Math.PI * generationProgress / 100 - Math.PI/2) * 50}%, 50% 50%)`
                  }}
                />
                
                {/* Progress text */}
                <div className="absolute inset-0 flex items-center justify-center">
                  <span className="text-white font-bold text-sm">
                    {Math.round(generationProgress)}%
                  </span>
                </div>
              </motion.div>
              
              <motion.div
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.2, duration: 0.4 }}
              >
                <motion.p 
                  className="text-gray-300 mb-2 text-lg font-medium"
                >
                  {generationProgress < 30 ? 'Analyzing your idea...' :
                   generationProgress < 70 ? 'Generating app concept...' :
                   generationProgress < 95 ? 'Finalizing details...' : 'Almost ready!'}
                </motion.p>
                <motion.p 
                  className="text-gray-500 text-sm"
                >
                  This usually takes 10-15 seconds
                </motion.p>
              </motion.div>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Your Projects Workspace Section */}
        <AnimatePresence>
          {!isGenerating && recentApps.length > 0 && (
            <motion.div 
              key="projects-workspace-section"
              className="mt-20 w-full"
              initial={{ opacity: 0, y: 40 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ 
                delay: 0.4, 
                duration: 0.6,
                ease: [0.23, 1, 0.32, 1]
              }}
            >
              <motion.div
                className="flex items-center justify-between mb-6"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.5, duration: 0.4 }}
              >
                <div>
                  <h2 className="text-2xl md:text-3xl font-bold text-white mb-2">
                    {user?.full_name ? `${user.full_name}'s Workspace` : "Your Workspace"}
                  </h2>
                </div>
                <div className="flex items-center gap-3">
                  <div className="flex items-center gap-2">
                    <Search className="w-4 h-4 text-gray-400" />
                    <Input 
                      placeholder="Search projects..." 
                      className="w-48 bg-[#1c1c1c] border-gray-700 text-white text-sm h-8"
                    />
                  </div>
                  <select className="bg-[#1c1c1c] border border-gray-700 rounded-md px-3 py-1 text-white text-sm h-8">
                    <option>Last edited</option>
                    <option>Newest first</option>
                    <option>Oldest first</option>
                  </select>
                  <select className="bg-[#1c1c1c] border border-gray-700 rounded-md px-3 py-1 text-white text-sm h-8">
                    <option>Newest first</option>
                    <option>Popular</option>
                    <option>Recent</option>
                  </select>
                  <select className="bg-[#1c1c1c] border border-gray-700 rounded-md px-3 py-1 text-white text-sm h-8">
                    <option>All creators</option>
                    <option>Me only</option>
                    <option>Collaborators</option>
                  </select>
                  <Link to={createPageUrl("MyApps")}>
                    <Button variant="outline" size="sm" className="border-gray-700 text-white hover:bg-gray-800 text-xs h-8">
                      View All
                    </Button>
                  </Link>
                </div>
              </motion.div>
              
              <div className="grid grid-cols-4 gap-4 mb-6">
                {recentApps.map((app, index) => (
                  <motion.div 
                    key={app.id} 
                    className="bg-[#1c1c1c] border border-gray-800 rounded-xl p-3 hover:border-gray-700 transition-colors cursor-pointer group"
                    initial={{ opacity: 0, y: 30, scale: 0.9 }}
                    animate={{ opacity: 1, y: 0, scale: 1 }}
                    transition={{ 
                      delay: 0.6 + (index * 0.1), 
                      duration: 0.5,
                      ease: [0.23, 1, 0.32, 1]
                    }}
                    onClick={() => viewApp(app)}
                  >
                    {/* Project Preview */}
                    <div className="aspect-video bg-gradient-to-br from-gray-800 to-gray-900 rounded-lg mb-3 relative overflow-hidden">
                      <div className="absolute inset-0 bg-gradient-to-br from-blue-500/20 to-purple-500/20"></div>
                      <div className="absolute top-2 left-2 text-xs text-white/70">
                        {app.app_name}
                      </div>
                      <div className="absolute bottom-2 right-2">
                        <div className="w-6 h-6 bg-cyan-500 rounded-full flex items-center justify-center">
                          <div className="w-2 h-2 bg-white rounded-full"></div>
                        </div>
                      </div>
                    </div>
                    
                    {/* Project Info */}
                    <div className="flex items-center gap-2 mb-1">
                      <div className="w-4 h-4 bg-cyan-500 rounded-full flex items-center justify-center">
                        <span className="text-white text-xs font-bold">!</span>
                      </div>
                      <h3 className="font-medium text-white text-sm truncate">{app.app_name}</h3>
                    </div>
                    <p className="text-gray-400 text-xs">
                      Edited {new Date(app.updated_date || app.created_date).toLocaleDateString()}
                    </p>
                  </motion.div>
                ))}
              </div>

              <div className="text-center">
                <Button variant="outline" className="border-gray-700 text-white hover:bg-gray-800">
                  Show more
                </Button>
              </div>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Community Section */}
        <AnimatePresence>
          {!isGenerating && (
            <motion.div 
              key="community-section"
              className="mt-16 w-full"
              initial={{ opacity: 0, y: 40 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ 
                delay: recentApps.length > 0 ? 1.0 : 0.6, 
                duration: 0.6,
                ease: [0.23, 1, 0.32, 1]
              }}
            >
              <div className="flex items-center justify-between mb-6">
                <motion.div
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: recentApps.length > 0 ? 1.1 : 0.7, duration: 0.4 }}
                >
                  <h2 className="text-2xl md:text-3xl font-bold text-white mb-2">
                    From the Community
                  </h2>
                </motion.div>
                <div className="flex items-center gap-3">
                  <div className="flex gap-2">
                    {['Popular', 'Discover', 'Internal Tools', 'Website', 'Personal', 'Consumer App', 'B2B App', 'Prototype'].map((filter) => (
                      <button 
                        key={filter}
                        className="px-3 py-1 text-xs bg-[#1c1c1c] border border-gray-700 rounded-md text-gray-400 hover:text-white hover:bg-gray-800 transition-colors"
                      >
                        {filter}
                      </button>
                    ))}
                  </div>
                  <Link to={createPageUrl("Community")}>
                    <Button variant="outline" size="sm" className="border-gray-700 text-white hover:bg-gray-800 text-xs h-8">
                      View All
                    </Button>
                  </Link>
                </div>
              </div>
              
              <div className="grid grid-cols-4 gap-4">
                {communityProjects.map((project, index) => (
                  <motion.div 
                    key={project.id} 
                    className="bg-[#1c1c1c] border border-gray-800 rounded-xl p-3 hover:border-gray-700 transition-colors cursor-pointer group"
                    initial={{ opacity: 0, y: 30, scale: 0.9 }}
                    animate={{ opacity: 1, y: 0, scale: 1 }}
                    transition={{ 
                      delay: (recentApps.length > 0 ? 1.2 : 0.8) + (index * 0.08), 
                      duration: 0.5,
                      ease: [0.23, 1, 0.32, 1]
                    }}
                  >
                    {/* Project Preview */}
                    <div className={`aspect-video bg-gradient-to-br ${project.preview_bg} rounded-lg mb-3 relative overflow-hidden`}>
                      <div className="absolute inset-0 bg-black/20"></div>
                      <div className="absolute top-2 left-2 text-xs text-white/90 font-medium">
                        {project.name}
                      </div>
                      <div className="absolute bottom-2 right-2">
                        <span className="text-xs text-white/70 bg-black/30 px-2 py-1 rounded">
                          {project.category}
                        </span>
                      </div>
                    </div>
                    
                    {/* Project Info */}
                    <div className="flex items-center gap-2 mb-1">
                      <div className="w-6 h-6 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center text-white font-semibold text-xs">
                        {project.avatar}
                      </div>
                      <div>
                        <h3 className="font-medium text-white text-sm">{project.name}</h3>
                        <p className="text-gray-400 text-xs">{project.likes} Remixes</p>
                      </div>
                    </div>
                  </motion.div>
                ))}
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </motion.div>
  );
}
