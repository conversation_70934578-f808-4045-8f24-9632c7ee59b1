import React, { useState, useCallback, useRef, useEffect } from 'react';
import { 
  X, 
  File, 
  Code, 
  FileText, 
  Image, 
  Settings,
  ChevronLeft,
  ChevronRight,
  MoreHorizontal
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';

const FileTabs = ({
  openFiles = [],
  activeFile,
  onFileSelect,
  onFileClose,
  onFileCloseAll,
  onFileCloseOthers,
  unsavedFiles = new Set(),
  className = ''
}) => {
  const [scrollPosition, setScrollPosition] = useState(0);
  const [showScrollButtons, setShowScrollButtons] = useState(false);
  const tabsContainerRef = useRef(null);
  const tabsRef = useRef(null);

  // Get file icon based on extension
  const getFileIcon = useCallback((fileName) => {
    const ext = fileName.split('.').pop()?.toLowerCase();
    switch (ext) {
      case 'js':
      case 'jsx':
      case 'ts':
      case 'tsx':
        return <Code className="w-3 h-3 text-yellow-500" />;
      case 'css':
      case 'scss':
      case 'sass':
        return <FileText className="w-3 h-3 text-blue-500" />;
      case 'html':
        return <FileText className="w-3 h-3 text-orange-500" />;
      case 'json':
        return <Settings className="w-3 h-3 text-green-500" />;
      case 'png':
      case 'jpg':
      case 'jpeg':
      case 'gif':
      case 'svg':
        return <Image className="w-3 h-3 text-purple-500" />;
      default:
        return <File className="w-3 h-3 text-gray-500" />;
    }
  }, []);

  // Get file name from path
  const getFileName = useCallback((filePath) => {
    return filePath.split('/').pop() || filePath;
  }, []);

  // Check if scrolling is needed
  const checkScrollNeeded = useCallback(() => {
    if (tabsContainerRef.current && tabsRef.current) {
      const containerWidth = tabsContainerRef.current.offsetWidth;
      const tabsWidth = tabsRef.current.scrollWidth;
      setShowScrollButtons(tabsWidth > containerWidth);
    }
  }, []);

  // Handle scroll
  const handleScroll = useCallback((direction) => {
    if (tabsRef.current) {
      const scrollAmount = 200;
      const newPosition = direction === 'left' 
        ? Math.max(0, scrollPosition - scrollAmount)
        : scrollPosition + scrollAmount;
      
      setScrollPosition(newPosition);
      tabsRef.current.scrollLeft = newPosition;
    }
  }, [scrollPosition]);

  // Handle file selection
  const handleFileSelect = useCallback((filePath) => {
    onFileSelect?.(filePath);
  }, [onFileSelect]);

  // Handle file close
  const handleFileClose = useCallback((filePath, event) => {
    event.stopPropagation();
    onFileClose?.(filePath);
  }, [onFileClose]);

  // Handle middle click to close
  const handleTabMouseDown = useCallback((filePath, event) => {
    if (event.button === 1) { // Middle mouse button
      event.preventDefault();
      onFileClose?.(filePath);
    }
  }, [onFileClose]);

  // Handle context menu actions
  const handleCloseOthers = useCallback((filePath) => {
    onFileCloseOthers?.(filePath);
  }, [onFileCloseOthers]);

  const handleCloseAll = useCallback(() => {
    onFileCloseAll?.();
  }, [onFileCloseAll]);

  // Update scroll buttons visibility when files change
  useEffect(() => {
    checkScrollNeeded();
    const resizeObserver = new ResizeObserver(checkScrollNeeded);
    if (tabsContainerRef.current) {
      resizeObserver.observe(tabsContainerRef.current);
    }
    return () => resizeObserver.disconnect();
  }, [openFiles, checkScrollNeeded]);

  // Scroll active tab into view
  useEffect(() => {
    if (activeFile && tabsRef.current) {
      const activeTab = tabsRef.current.querySelector(`[data-file-path="${activeFile}"]`);
      if (activeTab) {
        activeTab.scrollIntoView({ behavior: 'smooth', block: 'nearest', inline: 'nearest' });
      }
    }
  }, [activeFile]);

  if (openFiles.length === 0) {
    return (
      <div className={`h-10 bg-gray-800 border-b border-gray-700 flex items-center px-4 ${className}`}>
        <span className="text-sm text-gray-500">No files open</span>
      </div>
    );
  }

  return (
    <div className={`h-10 bg-gray-800 border-b border-gray-700 flex items-center ${className}`}>
      {/* Left scroll button */}
      {showScrollButtons && (
        <Button
          size="sm"
          variant="ghost"
          onClick={() => handleScroll('left')}
          className="h-8 w-8 p-0 flex-shrink-0"
          disabled={scrollPosition === 0}
        >
          <ChevronLeft className="w-4 h-4" />
        </Button>
      )}

      {/* Tabs container */}
      <div 
        ref={tabsContainerRef}
        className="flex-1 overflow-hidden"
      >
        <div 
          ref={tabsRef}
          className="flex overflow-x-auto scrollbar-hide"
          style={{ scrollBehavior: 'smooth' }}
        >
          {openFiles.map((filePath) => {
            const fileName = getFileName(filePath);
            const isActive = activeFile === filePath;
            const isUnsaved = unsavedFiles.has(filePath);

            return (
              <div
                key={filePath}
                data-file-path={filePath}
                className={`
                  flex items-center gap-2 px-3 py-2 cursor-pointer border-r border-gray-700 
                  min-w-0 max-w-48 flex-shrink-0 group relative
                  ${isActive 
                    ? 'bg-gray-900 text-white border-b-2 border-blue-500' 
                    : 'bg-gray-800 text-gray-300 hover:bg-gray-700'
                  }
                `}
                onClick={() => handleFileSelect(filePath)}
                onMouseDown={(e) => handleTabMouseDown(filePath, e)}
                title={filePath}
              >
                {/* File icon */}
                {getFileIcon(fileName)}

                {/* File name */}
                <span className="text-sm truncate flex-1 min-w-0">
                  {fileName}
                </span>

                {/* Unsaved indicator */}
                {isUnsaved && (
                  <div className="w-2 h-2 bg-orange-500 rounded-full flex-shrink-0" />
                )}

                {/* Close button */}
                <Button
                  size="sm"
                  variant="ghost"
                  onClick={(e) => handleFileClose(filePath, e)}
                  className="h-4 w-4 p-0 opacity-0 group-hover:opacity-100 hover:bg-gray-600 flex-shrink-0"
                >
                  <X className="w-3 h-3" />
                </Button>

                {/* Context menu */}
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <div 
                      className="absolute inset-0 opacity-0"
                      onContextMenu={(e) => e.preventDefault()}
                    />
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="start">
                    <DropdownMenuItem onClick={() => handleFileClose(filePath)}>
                      Close
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => handleCloseOthers(filePath)}>
                      Close Others
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={handleCloseAll}>
                      Close All
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            );
          })}
        </div>
      </div>

      {/* Right scroll button */}
      {showScrollButtons && (
        <Button
          size="sm"
          variant="ghost"
          onClick={() => handleScroll('right')}
          className="h-8 w-8 p-0 flex-shrink-0"
        >
          <ChevronRight className="w-4 h-4" />
        </Button>
      )}

      {/* Tab actions menu */}
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            size="sm"
            variant="ghost"
            className="h-8 w-8 p-0 flex-shrink-0"
          >
            <MoreHorizontal className="w-4 h-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          <DropdownMenuItem onClick={handleCloseAll}>
            Close All Tabs
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => handleCloseOthers(activeFile)}>
            Close Other Tabs
          </DropdownMenuItem>
          <DropdownMenuItem 
            onClick={() => {
              const unsavedList = Array.from(unsavedFiles);
              unsavedList.forEach(file => onFileClose?.(file));
            }}
            disabled={unsavedFiles.size === 0}
          >
            Close Unsaved Tabs ({unsavedFiles.size})
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
};

export default FileTabs;
