import React, { useState, useRef, useEffect, useCallback } from 'react';
import Editor from '@monaco-editor/react';
import { 
  Play, 
  Save, 
  RotateCcw, 
  Settings, 
  Maximize2, 
  Minimize2,
  Lightbulb,
  Zap
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';

const CodeEditor = ({
  value = '',
  onChange,
  language = 'javascript',
  fileName = 'untitled.js',
  onSave,
  onRun,
  onAIAssist,
  className = '',
  readOnly = false,
  theme = 'vs-dark'
}) => {
  const editorRef = useRef(null);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const [editorSettings, setEditorSettings] = useState({
    fontSize: 14,
    wordWrap: 'on',
    minimap: { enabled: true },
    lineNumbers: 'on',
    formatOnPaste: true,
    formatOnType: true
  });

  // Handle editor mount
  const handleEditorDidMount = useCallback((editor, monaco) => {
    editorRef.current = editor;

    // Configure Monaco for better JavaScript/React support
    monaco.languages.typescript.javascriptDefaults.setCompilerOptions({
      target: monaco.languages.typescript.ScriptTarget.Latest,
      allowNonTsExtensions: true,
      moduleResolution: monaco.languages.typescript.ModuleResolutionKind.NodeJs,
      module: monaco.languages.typescript.ModuleKind.CommonJS,
      noEmit: true,
      esModuleInterop: true,
      jsx: monaco.languages.typescript.JsxEmit.React,
      reactNamespace: 'React',
      allowJs: true,
      typeRoots: ['node_modules/@types']
    });

    // Add React types
    monaco.languages.typescript.javascriptDefaults.addExtraLib(`
      declare module 'react' {
        export interface Component<P = {}, S = {}> {}
        export function useState<T>(initial: T): [T, (value: T) => void];
        export function useEffect(effect: () => void, deps?: any[]): void;
        export function useCallback<T extends (...args: any[]) => any>(callback: T, deps: any[]): T;
        export function useMemo<T>(factory: () => T, deps: any[]): T;
        export const Fragment: any;
      }
    `, 'react.d.ts');

    // Add keyboard shortcuts
    editor.addCommand(monaco.KeyMod.CtrlCmd | monaco.KeyCode.KeyS, () => {
      handleSave();
    });

    editor.addCommand(monaco.KeyMod.CtrlCmd | monaco.KeyCode.Enter, () => {
      handleRun();
    });

    editor.addCommand(monaco.KeyMod.CtrlCmd | monaco.KeyCode.KeyI, () => {
      handleAIAssist();
    });

    // Auto-completion for common React patterns
    monaco.languages.registerCompletionItemProvider('javascript', {
      provideCompletionItems: (model, position) => {
        const suggestions = [
          {
            label: 'useState',
            kind: monaco.languages.CompletionItemKind.Snippet,
            insertText: 'const [${1:state}, set${1/(.*)/${1:/capitalize}/}] = useState(${2:initialValue});',
            insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
            documentation: 'React useState hook'
          },
          {
            label: 'useEffect',
            kind: monaco.languages.CompletionItemKind.Snippet,
            insertText: 'useEffect(() => {\n\t${1:// effect}\n\treturn () => {\n\t\t${2:// cleanup}\n\t};\n}, [${3:dependencies}]);',
            insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
            documentation: 'React useEffect hook'
          },
          {
            label: 'component',
            kind: monaco.languages.CompletionItemKind.Snippet,
            insertText: 'const ${1:ComponentName} = ({ ${2:props} }) => {\n\treturn (\n\t\t<div>\n\t\t\t${3:// component content}\n\t\t</div>\n\t);\n};\n\nexport default ${1:ComponentName};',
            insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
            documentation: 'React functional component'
          }
        ];
        return { suggestions };
      }
    });

  }, []);

  // Handle content change
  const handleEditorChange = useCallback((newValue) => {
    setHasUnsavedChanges(true);
    onChange?.(newValue);
  }, [onChange]);

  // Handle save
  const handleSave = useCallback(() => {
    if (onSave) {
      onSave(value, fileName);
      setHasUnsavedChanges(false);
    }
  }, [onSave, value, fileName]);

  // Handle run
  const handleRun = useCallback(() => {
    onRun?.(value, fileName);
  }, [onRun, value, fileName]);

  // Handle AI assist
  const handleAIAssist = useCallback(() => {
    const editor = editorRef.current;
    if (editor && onAIAssist) {
      const selection = editor.getSelection();
      const selectedText = editor.getModel().getValueInRange(selection);
      const cursorPosition = editor.getPosition();
      
      onAIAssist({
        selectedText,
        fullCode: value,
        fileName,
        cursorPosition,
        language
      });
    }
  }, [onAIAssist, value, fileName, language]);

  // Toggle fullscreen
  const toggleFullscreen = useCallback(() => {
    setIsFullscreen(prev => !prev);
  }, []);

  // Format code
  const formatCode = useCallback(() => {
    const editor = editorRef.current;
    if (editor) {
      editor.getAction('editor.action.formatDocument').run();
    }
  }, []);

  // Get language from file extension
  const getLanguageFromFileName = useCallback((fileName) => {
    const ext = fileName.split('.').pop()?.toLowerCase();
    switch (ext) {
      case 'js':
      case 'jsx':
        return 'javascript';
      case 'ts':
      case 'tsx':
        return 'typescript';
      case 'css':
        return 'css';
      case 'scss':
      case 'sass':
        return 'scss';
      case 'html':
        return 'html';
      case 'json':
        return 'json';
      case 'md':
        return 'markdown';
      default:
        return 'plaintext';
    }
  }, []);

  const currentLanguage = language || getLanguageFromFileName(fileName);

  return (
    <div className={`flex flex-col h-full bg-gray-900 ${className} ${isFullscreen ? 'fixed inset-0 z-50' : ''}`}>
      {/* Editor Header */}
      <div className="flex items-center justify-between p-2 bg-gray-800 border-b border-gray-700">
        <div className="flex items-center gap-2">
          <span className="text-sm text-gray-300">{fileName}</span>
          {hasUnsavedChanges && (
            <Badge variant="secondary" className="text-xs">
              Unsaved
            </Badge>
          )}
          <Badge variant="outline" className="text-xs">
            {currentLanguage}
          </Badge>
        </div>
        
        <div className="flex items-center gap-1">
          {onAIAssist && (
            <Button
              size="sm"
              variant="ghost"
              onClick={handleAIAssist}
              className="h-7 px-2"
              title="AI Assist (Ctrl+I)"
            >
              <Lightbulb className="w-3 h-3 mr-1" />
              AI
            </Button>
          )}
          
          <Button
            size="sm"
            variant="ghost"
            onClick={formatCode}
            className="h-7 px-2"
            title="Format Code"
          >
            <Zap className="w-3 h-3" />
          </Button>
          
          {onSave && (
            <Button
              size="sm"
              variant="ghost"
              onClick={handleSave}
              className="h-7 px-2"
              title="Save (Ctrl+S)"
              disabled={!hasUnsavedChanges}
            >
              <Save className="w-3 h-3" />
            </Button>
          )}
          
          {onRun && (
            <Button
              size="sm"
              variant="ghost"
              onClick={handleRun}
              className="h-7 px-2"
              title="Run (Ctrl+Enter)"
            >
              <Play className="w-3 h-3" />
            </Button>
          )}
          
          <Button
            size="sm"
            variant="ghost"
            onClick={toggleFullscreen}
            className="h-7 px-2"
            title="Toggle Fullscreen"
          >
            {isFullscreen ? (
              <Minimize2 className="w-3 h-3" />
            ) : (
              <Maximize2 className="w-3 h-3" />
            )}
          </Button>
        </div>
      </div>

      {/* Monaco Editor */}
      <div className="flex-1">
        <Editor
          value={value}
          language={currentLanguage}
          theme={theme}
          onChange={handleEditorChange}
          onMount={handleEditorDidMount}
          options={{
            ...editorSettings,
            readOnly,
            automaticLayout: true,
            scrollBeyondLastLine: false,
            smoothScrolling: true,
            cursorBlinking: 'smooth',
            renderLineHighlight: 'all',
            selectOnLineNumbers: true,
            roundedSelection: false,
            contextmenu: true,
            mouseWheelZoom: true,
            quickSuggestions: {
              other: true,
              comments: true,
              strings: true
            },
            parameterHints: {
              enabled: true
            },
            suggestOnTriggerCharacters: true,
            acceptSuggestionOnEnter: 'on',
            tabCompletion: 'on',
            wordBasedSuggestions: true,
            folding: true,
            foldingStrategy: 'indentation',
            showFoldingControls: 'always',
            bracketPairColorization: {
              enabled: true
            }
          }}
        />
      </div>

      {/* Status Bar */}
      <div className="flex items-center justify-between px-3 py-1 bg-gray-800 border-t border-gray-700 text-xs text-gray-400">
        <div className="flex items-center gap-4">
          <span>Lines: {value.split('\n').length}</span>
          <span>Characters: {value.length}</span>
          <span>Language: {currentLanguage}</span>
        </div>
        
        <div className="flex items-center gap-2">
          {readOnly && (
            <Badge variant="outline" className="text-xs">
              Read Only
            </Badge>
          )}
          <span>UTF-8</span>
        </div>
      </div>
    </div>
  );
};

export default CodeEditor;
