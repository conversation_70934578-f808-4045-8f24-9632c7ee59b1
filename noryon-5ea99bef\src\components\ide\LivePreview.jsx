import React, { useState, useEffect, useRef, useCallback } from 'react';
import {
  Eye,
  Smartphone,
  Monitor,
  Tablet,
  RotateCcw,
  ExternalLink,
  Maximize2,
  Minimize2,
  AlertCircle,
  CheckCircle,
  Loader2,
  Play,
  Square,
  Zap
} from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import BackendService from '@/api/backendService';

const LivePreview = ({
  fileStructure = {},
  activeFile,
  onError,
  className = ''
}) => {
  const [previewMode, setPreviewMode] = useState('desktop'); // desktop, tablet, mobile
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [previewUrl, setPreviewUrl] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [lastUpdate, setLastUpdate] = useState(null);
  const [devServerStatus, setDevServerStatus] = useState('stopped'); // stopped, starting, running, error
  const [devServerProcessId, setDevServerProcessId] = useState(null);
  const [devServerPort, setDevServerPort] = useState(3000);
  const [useDevServer, setUseDevServer] = useState(false);
  const iframeRef = useRef(null);

  // Generate preview HTML from file structure
  const generatePreviewHTML = useCallback(() => {
    try {
      const htmlFile = fileStructure['index.html'];
      const mainJsx = fileStructure['src/main.jsx'] || fileStructure['src/App.jsx'];
      const appJsx = fileStructure['src/App.jsx'];
      const appCss = fileStructure['src/App.css'] || fileStructure['src/index.css'];
      const indexCss = fileStructure['src/index.css'];

      if (!htmlFile && !mainJsx && !appJsx) {
        return null;
      }

      // Create a complete HTML document
      let html = '';

      if (htmlFile) {
        // Use existing HTML file as base
        html = htmlFile;
        
        // Inject CSS if available
        if (appCss || indexCss) {
          const cssContent = (indexCss || '') + '\n' + (appCss || '');
          html = html.replace('</head>', `<style>${cssContent}</style>\n</head>`);
        }

        // If it's a React app, create a simple React preview
        if (mainJsx || appJsx) {
          const reactCode = appJsx || mainJsx;
          
          // Extract JSX content for preview
          const jsxMatch = reactCode.match(/return\s*\(([\s\S]*?)\);?\s*}/);
          if (jsxMatch) {
            let jsxContent = jsxMatch[1];
            
            // Simple JSX to HTML conversion for preview
            jsxContent = jsxContent
              .replace(/className=/g, 'class=')
              .replace(/\{[^}]*\}/g, '') // Remove JSX expressions
              .replace(/<([A-Z][a-zA-Z]*)[^>]*>/g, '<div>') // Convert components to divs
              .replace(/<\/[A-Z][a-zA-Z]*>/g, '</div>');
            
            html = html.replace('<div id="root"></div>', `<div id="root">${jsxContent}</div>`);
          }
        }
      } else {
        // Create HTML from scratch
        html = `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Live Preview</title>
    <style>
        body {
            margin: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            background: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        ${appCss || ''}
        ${indexCss || ''}
    </style>
</head>
<body>
    <div id="root">
        <div class="container">
            <h1>Live Preview</h1>
            <p>Your website preview will appear here as you code.</p>
        </div>
    </div>
</body>
</html>`;
      }

      return html;
    } catch (error) {
      console.error('Error generating preview HTML:', error);
      setError('Failed to generate preview');
      return null;
    }
  }, [fileStructure]);

  // Start development server
  const startDevServer = useCallback(async () => {
    setDevServerStatus('starting');
    setError(null);

    try {
      // Check if project has package.json and dev script
      const analysis = await BackendService.analyzeProject();

      if (!analysis.hasPackageJson) {
        throw new Error('No package.json found. Cannot start development server.');
      }

      const result = await BackendService.startDevServer('.', devServerPort);

      setDevServerProcessId(result.processId);
      setDevServerStatus('running');
      setPreviewUrl(result.url);
      setUseDevServer(true);
      setLastUpdate(new Date());

    } catch (error) {
      setError(`Failed to start dev server: ${error.message}`);
      setDevServerStatus('error');
      onError?.(error.message);
    }
  }, [devServerPort, onError]);

  // Stop development server
  const stopDevServer = useCallback(async () => {
    if (!devServerProcessId) return;

    try {
      await BackendService.stopDevServer(devServerProcessId);
      setDevServerStatus('stopped');
      setDevServerProcessId(null);
      setUseDevServer(false);
      setPreviewUrl(null);
    } catch (error) {
      setError(`Failed to stop dev server: ${error.message}`);
    }
  }, [devServerProcessId]);

  // Update preview when files change
  useEffect(() => {
    if (Object.keys(fileStructure).length > 0) {
      if (useDevServer && devServerStatus === 'running') {
        // Dev server handles hot reload automatically
        setLastUpdate(new Date());
        return;
      }

      // Fallback to static preview
      setIsLoading(true);
      setError(null);

      const html = generatePreviewHTML();
      if (html) {
        const blob = new Blob([html], { type: 'text/html' });
        const url = URL.createObjectURL(blob);

        // Clean up previous URL
        if (previewUrl && !useDevServer) {
          URL.revokeObjectURL(previewUrl);
        }

        if (!useDevServer) {
          setPreviewUrl(url);
        }
        setLastUpdate(new Date());
      }

      setIsLoading(false);
    }
  }, [fileStructure, generatePreviewHTML, previewUrl, useDevServer, devServerStatus]);

  // Cleanup URL on unmount
  useEffect(() => {
    return () => {
      if (previewUrl) {
        URL.revokeObjectURL(previewUrl);
      }
    };
  }, [previewUrl]);

  // Handle iframe load
  const handleIframeLoad = useCallback(() => {
    setIsLoading(false);
    setError(null);
  }, []);

  // Handle iframe error
  const handleIframeError = useCallback(() => {
    setIsLoading(false);
    setError('Failed to load preview');
    onError?.('Preview failed to load');
  }, [onError]);

  // Refresh preview
  const refreshPreview = useCallback(() => {
    if (iframeRef.current) {
      iframeRef.current.src = iframeRef.current.src;
    }
  }, []);

  // Open in new tab
  const openInNewTab = useCallback(() => {
    if (previewUrl) {
      window.open(previewUrl, '_blank');
    }
  }, [previewUrl]);

  // Toggle fullscreen
  const toggleFullscreen = useCallback(() => {
    setIsFullscreen(prev => !prev);
  }, []);

  // Get preview dimensions based on mode
  const getPreviewDimensions = () => {
    switch (previewMode) {
      case 'mobile':
        return { width: '375px', height: '667px' };
      case 'tablet':
        return { width: '768px', height: '1024px' };
      default:
        return { width: '100%', height: '100%' };
    }
  };

  const dimensions = getPreviewDimensions();

  return (
    <div className={`flex flex-col h-full bg-gray-900 ${className} ${isFullscreen ? 'fixed inset-0 z-50' : ''}`}>
      {/* Preview Header */}
      <div className="flex items-center justify-between p-3 bg-gray-800 border-b border-gray-700">
        <div className="flex items-center gap-2">
          <Eye className="w-4 h-4 text-blue-400" />
          <span className="text-sm text-gray-300">Live Preview</span>

          {/* Dev Server Status */}
          {devServerStatus === 'running' && (
            <Badge variant="outline" className="text-xs text-green-400 border-green-400">
              <div className="w-2 h-2 bg-green-400 rounded-full mr-1" />
              Dev Server
            </Badge>
          )}
          {devServerStatus === 'starting' && (
            <Badge variant="outline" className="text-xs text-yellow-400 border-yellow-400">
              <Loader2 className="w-3 h-3 animate-spin mr-1" />
              Starting
            </Badge>
          )}
          {devServerStatus === 'error' && (
            <Badge variant="outline" className="text-xs text-red-400 border-red-400">
              <AlertCircle className="w-3 h-3 mr-1" />
              Error
            </Badge>
          )}

          {lastUpdate && (
            <Badge variant="outline" className="text-xs">
              Updated {lastUpdate.toLocaleTimeString()}
            </Badge>
          )}
          {isLoading && (
            <Loader2 className="w-4 h-4 animate-spin text-blue-400" />
          )}
          {error && (
            <AlertCircle className="w-4 h-4 text-red-400" />
          )}
          {!error && !isLoading && previewUrl && !useDevServer && (
            <CheckCircle className="w-4 h-4 text-green-400" />
          )}
        </div>
        
        <div className="flex items-center gap-1">
          {/* Dev Server Controls */}
          <div className="flex items-center gap-1 mr-2">
            {devServerStatus === 'stopped' && (
              <Button
                size="sm"
                variant="ghost"
                onClick={startDevServer}
                className="h-7 px-2 text-green-400 hover:text-green-300"
                title="Start Development Server"
              >
                <Play className="w-3 h-3" />
              </Button>
            )}
            {devServerStatus === 'running' && (
              <Button
                size="sm"
                variant="ghost"
                onClick={stopDevServer}
                className="h-7 px-2 text-red-400 hover:text-red-300"
                title="Stop Development Server"
              >
                <Square className="w-3 h-3" />
              </Button>
            )}
            {devServerStatus === 'starting' && (
              <Button
                size="sm"
                variant="ghost"
                disabled
                className="h-7 px-2"
                title="Starting..."
              >
                <Loader2 className="w-3 h-3 animate-spin" />
              </Button>
            )}
          </div>

          {/* Device Mode Buttons */}
          <div className="flex items-center gap-1 mr-2">
            <Button
              size="sm"
              variant={previewMode === 'desktop' ? 'default' : 'ghost'}
              onClick={() => setPreviewMode('desktop')}
              className="h-7 px-2"
              title="Desktop View"
            >
              <Monitor className="w-3 h-3" />
            </Button>
            <Button
              size="sm"
              variant={previewMode === 'tablet' ? 'default' : 'ghost'}
              onClick={() => setPreviewMode('tablet')}
              className="h-7 px-2"
              title="Tablet View"
            >
              <Tablet className="w-3 h-3" />
            </Button>
            <Button
              size="sm"
              variant={previewMode === 'mobile' ? 'default' : 'ghost'}
              onClick={() => setPreviewMode('mobile')}
              className="h-7 px-2"
              title="Mobile View"
            >
              <Smartphone className="w-3 h-3" />
            </Button>
          </div>
          
          <Button
            size="sm"
            variant="ghost"
            onClick={refreshPreview}
            className="h-7 px-2"
            title="Refresh Preview"
          >
            <RotateCcw className="w-3 h-3" />
          </Button>
          
          <Button
            size="sm"
            variant="ghost"
            onClick={openInNewTab}
            className="h-7 px-2"
            title="Open in New Tab"
            disabled={!previewUrl}
          >
            <ExternalLink className="w-3 h-3" />
          </Button>
          
          <Button
            size="sm"
            variant="ghost"
            onClick={toggleFullscreen}
            className="h-7 px-2"
            title="Toggle Fullscreen"
          >
            {isFullscreen ? (
              <Minimize2 className="w-3 h-3" />
            ) : (
              <Maximize2 className="w-3 h-3" />
            )}
          </Button>
        </div>
      </div>

      {/* Preview Content */}
      <div className="flex-1 bg-gray-100 flex items-center justify-center p-4">
        {error ? (
          <div className="text-center text-gray-600">
            <AlertCircle className="w-16 h-16 mx-auto mb-4 opacity-50" />
            <p className="text-lg font-medium mb-2">Preview Error</p>
            <p className="text-sm">{error}</p>
            <Button 
              onClick={refreshPreview} 
              className="mt-4"
              variant="outline"
            >
              Try Again
            </Button>
          </div>
        ) : !previewUrl ? (
          <div className="text-center text-gray-600">
            <Eye className="w-16 h-16 mx-auto mb-4 opacity-50" />
            <p className="text-lg font-medium mb-2">No Preview Available</p>
            <p className="text-sm">Start coding to see your website preview</p>
          </div>
        ) : (
          <div 
            className="bg-white rounded-lg shadow-lg overflow-hidden transition-all duration-300"
            style={{
              width: dimensions.width,
              height: dimensions.height,
              maxWidth: '100%',
              maxHeight: '100%'
            }}
          >
            <iframe
              ref={iframeRef}
              src={previewUrl}
              className="w-full h-full border-0"
              onLoad={handleIframeLoad}
              onError={handleIframeError}
              title="Live Preview"
              sandbox="allow-scripts allow-same-origin"
            />
          </div>
        )}
      </div>

      {/* Preview Footer */}
      <div className="flex items-center justify-between px-3 py-2 bg-gray-800 border-t border-gray-700 text-xs text-gray-400">
        <div className="flex items-center gap-4">
          <span>Mode: {previewMode}</span>
          <span>Size: {dimensions.width} × {dimensions.height}</span>
        </div>
        
        <div className="flex items-center gap-2">
          {previewUrl && (
            <Badge variant="outline" className="text-xs">
              Live
            </Badge>
          )}
        </div>
      </div>
    </div>
  );
};

export default LivePreview;
