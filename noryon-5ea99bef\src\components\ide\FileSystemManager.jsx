import React, { useState, useCallback, useEffect } from 'react';
import { 
  Folder, 
  File, 
  Plus, 
  Trash2, 
  Edit3, 
  Save, 
  X,
  ChevronRight,
  ChevronDown,
  FileText,
  Code,
  Image,
  Settings
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';

const FileSystemManager = ({ 
  fileStructure = {}, 
  onFileSelect, 
  onFileCreate, 
  onFileDelete, 
  onFileRename,
  onFolderCreate,
  activeFile,
  className = ""
}) => {
  const [expandedFolders, setExpandedFolders] = useState(new Set(['src']));
  const [editingItem, setEditingItem] = useState(null);
  const [newItemName, setNewItemName] = useState('');
  const [creatingItem, setCreatingItem] = useState(null);

  // Get file icon based on extension
  const getFileIcon = (fileName) => {
    const ext = fileName.split('.').pop()?.toLowerCase();
    switch (ext) {
      case 'js':
      case 'jsx':
      case 'ts':
      case 'tsx':
        return <Code className="w-4 h-4 text-yellow-500" />;
      case 'css':
      case 'scss':
      case 'sass':
        return <FileText className="w-4 h-4 text-blue-500" />;
      case 'html':
        return <FileText className="w-4 h-4 text-orange-500" />;
      case 'json':
        return <Settings className="w-4 h-4 text-green-500" />;
      case 'png':
      case 'jpg':
      case 'jpeg':
      case 'gif':
      case 'svg':
        return <Image className="w-4 h-4 text-purple-500" />;
      default:
        return <File className="w-4 h-4 text-gray-500" />;
    }
  };

  // Toggle folder expansion
  const toggleFolder = useCallback((folderPath) => {
    setExpandedFolders(prev => {
      const newSet = new Set(prev);
      if (newSet.has(folderPath)) {
        newSet.delete(folderPath);
      } else {
        newSet.add(folderPath);
      }
      return newSet;
    });
  }, []);

  // Handle file selection
  const handleFileSelect = useCallback((filePath) => {
    onFileSelect?.(filePath);
  }, [onFileSelect]);

  // Start creating new item
  const startCreating = useCallback((parentPath, type) => {
    setCreatingItem({ parentPath, type });
    setNewItemName('');
  }, []);

  // Confirm creation
  const confirmCreate = useCallback(() => {
    if (!newItemName.trim() || !creatingItem) return;

    const fullPath = creatingItem.parentPath 
      ? `${creatingItem.parentPath}/${newItemName}`
      : newItemName;

    if (creatingItem.type === 'file') {
      onFileCreate?.(fullPath, '');
    } else {
      onFolderCreate?.(fullPath);
      setExpandedFolders(prev => new Set([...prev, fullPath]));
    }

    setCreatingItem(null);
    setNewItemName('');
  }, [newItemName, creatingItem, onFileCreate, onFolderCreate]);

  // Cancel creation
  const cancelCreate = useCallback(() => {
    setCreatingItem(null);
    setNewItemName('');
  }, []);

  // Start editing item name
  const startEditing = useCallback((itemPath) => {
    setEditingItem(itemPath);
    setNewItemName(itemPath.split('/').pop());
  }, []);

  // Confirm rename
  const confirmRename = useCallback(() => {
    if (!newItemName.trim() || !editingItem) return;

    const pathParts = editingItem.split('/');
    pathParts[pathParts.length - 1] = newItemName;
    const newPath = pathParts.join('/');

    onFileRename?.(editingItem, newPath);
    setEditingItem(null);
    setNewItemName('');
  }, [newItemName, editingItem, onFileRename]);

  // Cancel editing
  const cancelEdit = useCallback(() => {
    setEditingItem(null);
    setNewItemName('');
  }, []);

  // Handle delete
  const handleDelete = useCallback((itemPath) => {
    if (window.confirm(`Are you sure you want to delete ${itemPath}?`)) {
      onFileDelete?.(itemPath);
    }
  }, [onFileDelete]);

  // Build file tree structure
  const buildFileTree = useCallback((files) => {
    const tree = {};
    
    Object.keys(files).forEach(filePath => {
      const parts = filePath.split('/');
      let current = tree;
      
      parts.forEach((part, index) => {
        if (index === parts.length - 1) {
          // It's a file
          current[part] = { type: 'file', path: filePath, content: files[filePath] };
        } else {
          // It's a folder
          if (!current[part]) {
            current[part] = { type: 'folder', children: {} };
          }
          current = current[part].children;
        }
      });
    });
    
    return tree;
  }, []);

  // Render file tree recursively
  const renderFileTree = useCallback((tree, parentPath = '') => {
    return Object.entries(tree).map(([name, item]) => {
      const fullPath = parentPath ? `${parentPath}/${name}` : name;
      const isExpanded = expandedFolders.has(fullPath);
      const isActive = activeFile === fullPath;
      const isEditing = editingItem === fullPath;

      if (item.type === 'folder') {
        return (
          <div key={fullPath} className="select-none">
            <div 
              className="flex items-center gap-1 px-2 py-1 hover:bg-gray-700 cursor-pointer group"
              onClick={() => toggleFolder(fullPath)}
            >
              {isExpanded ? (
                <ChevronDown className="w-4 h-4 text-gray-400" />
              ) : (
                <ChevronRight className="w-4 h-4 text-gray-400" />
              )}
              <Folder className="w-4 h-4 text-blue-400" />
              {isEditing ? (
                <div className="flex items-center gap-1 flex-1">
                  <Input
                    value={newItemName}
                    onChange={(e) => setNewItemName(e.target.value)}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter') confirmRename();
                      if (e.key === 'Escape') cancelEdit();
                    }}
                    className="h-6 text-xs bg-gray-800 border-gray-600"
                    autoFocus
                  />
                  <Button size="sm" variant="ghost" onClick={confirmRename} className="h-6 w-6 p-0">
                    <Save className="w-3 h-3" />
                  </Button>
                  <Button size="sm" variant="ghost" onClick={cancelEdit} className="h-6 w-6 p-0">
                    <X className="w-3 h-3" />
                  </Button>
                </div>
              ) : (
                <>
                  <span className="text-sm text-gray-300 flex-1">{name}</span>
                  <div className="opacity-0 group-hover:opacity-100 flex items-center gap-1">
                    <Button
                      size="sm"
                      variant="ghost"
                      onClick={(e) => {
                        e.stopPropagation();
                        startCreating(fullPath, 'file');
                      }}
                      className="h-6 w-6 p-0"
                      title="New File"
                    >
                      <Plus className="w-3 h-3" />
                    </Button>
                    <Button
                      size="sm"
                      variant="ghost"
                      onClick={(e) => {
                        e.stopPropagation();
                        startEditing(fullPath);
                      }}
                      className="h-6 w-6 p-0"
                      title="Rename"
                    >
                      <Edit3 className="w-3 h-3" />
                    </Button>
                    <Button
                      size="sm"
                      variant="ghost"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleDelete(fullPath);
                      }}
                      className="h-6 w-6 p-0"
                      title="Delete"
                    >
                      <Trash2 className="w-3 h-3" />
                    </Button>
                  </div>
                </>
              )}
            </div>
            
            {/* Show creation input for new items in this folder */}
            {creatingItem?.parentPath === fullPath && (
              <div className="flex items-center gap-1 px-2 py-1 ml-5">
                {creatingItem.type === 'file' ? (
                  <File className="w-4 h-4 text-gray-400" />
                ) : (
                  <Folder className="w-4 h-4 text-blue-400" />
                )}
                <Input
                  value={newItemName}
                  onChange={(e) => setNewItemName(e.target.value)}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter') confirmCreate();
                    if (e.key === 'Escape') cancelCreate();
                  }}
                  placeholder={creatingItem.type === 'file' ? 'filename.js' : 'folder-name'}
                  className="h-6 text-xs bg-gray-800 border-gray-600 flex-1"
                  autoFocus
                />
                <Button size="sm" variant="ghost" onClick={confirmCreate} className="h-6 w-6 p-0">
                  <Save className="w-3 h-3" />
                </Button>
                <Button size="sm" variant="ghost" onClick={cancelCreate} className="h-6 w-6 p-0">
                  <X className="w-3 h-3" />
                </Button>
              </div>
            )}
            
            {isExpanded && (
              <div className="ml-4">
                {renderFileTree(item.children, fullPath)}
              </div>
            )}
          </div>
        );
      } else {
        // File item
        return (
          <div
            key={fullPath}
            className={`flex items-center gap-1 px-2 py-1 cursor-pointer group ${
              isActive ? 'bg-blue-600' : 'hover:bg-gray-700'
            }`}
            onClick={() => handleFileSelect(fullPath)}
          >
            {getFileIcon(name)}
            {isEditing ? (
              <div className="flex items-center gap-1 flex-1">
                <Input
                  value={newItemName}
                  onChange={(e) => setNewItemName(e.target.value)}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter') confirmRename();
                    if (e.key === 'Escape') cancelEdit();
                  }}
                  className="h-6 text-xs bg-gray-800 border-gray-600"
                  autoFocus
                />
                <Button size="sm" variant="ghost" onClick={confirmRename} className="h-6 w-6 p-0">
                  <Save className="w-3 h-3" />
                </Button>
                <Button size="sm" variant="ghost" onClick={cancelEdit} className="h-6 w-6 p-0">
                  <X className="w-3 h-3" />
                </Button>
              </div>
            ) : (
              <>
                <span className={`text-sm flex-1 ${isActive ? 'text-white' : 'text-gray-300'}`}>
                  {name}
                </span>
                <div className="opacity-0 group-hover:opacity-100 flex items-center gap-1">
                  <Button
                    size="sm"
                    variant="ghost"
                    onClick={(e) => {
                      e.stopPropagation();
                      startEditing(fullPath);
                    }}
                    className="h-6 w-6 p-0"
                    title="Rename"
                  >
                    <Edit3 className="w-3 h-3" />
                  </Button>
                  <Button
                    size="sm"
                    variant="ghost"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleDelete(fullPath);
                    }}
                    className="h-6 w-6 p-0"
                    title="Delete"
                  >
                    <Trash2 className="w-3 h-3" />
                  </Button>
                </div>
              </>
            )}
          </div>
        );
      }
    });
  }, [
    expandedFolders, 
    activeFile, 
    editingItem, 
    newItemName, 
    creatingItem,
    toggleFolder,
    handleFileSelect,
    startCreating,
    startEditing,
    confirmCreate,
    confirmRename,
    cancelCreate,
    cancelEdit,
    handleDelete,
    getFileIcon
  ]);

  const fileTree = buildFileTree(fileStructure);

  return (
    <div className={`bg-gray-800 text-white h-full flex flex-col ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between p-3 border-b border-gray-700">
        <h3 className="text-sm font-medium">Explorer</h3>
        <div className="flex items-center gap-1">
          <Button
            size="sm"
            variant="ghost"
            onClick={() => startCreating('', 'file')}
            className="h-6 w-6 p-0"
            title="New File"
          >
            <Plus className="w-3 h-3" />
          </Button>
          <Button
            size="sm"
            variant="ghost"
            onClick={() => startCreating('', 'folder')}
            className="h-6 w-6 p-0"
            title="New Folder"
          >
            <Folder className="w-3 h-3" />
          </Button>
        </div>
      </div>

      {/* File Tree */}
      <div className="flex-1 overflow-auto p-2">
        {/* Show creation input for root level items */}
        {creatingItem?.parentPath === '' && (
          <div className="flex items-center gap-1 px-2 py-1 mb-2">
            {creatingItem.type === 'file' ? (
              <File className="w-4 h-4 text-gray-400" />
            ) : (
              <Folder className="w-4 h-4 text-blue-400" />
            )}
            <Input
              value={newItemName}
              onChange={(e) => setNewItemName(e.target.value)}
              onKeyDown={(e) => {
                if (e.key === 'Enter') confirmCreate();
                if (e.key === 'Escape') cancelCreate();
              }}
              placeholder={creatingItem.type === 'file' ? 'filename.js' : 'folder-name'}
              className="h-6 text-xs bg-gray-800 border-gray-600 flex-1"
              autoFocus
            />
            <Button size="sm" variant="ghost" onClick={confirmCreate} className="h-6 w-6 p-0">
              <Save className="w-3 h-3" />
            </Button>
            <Button size="sm" variant="ghost" onClick={cancelCreate} className="h-6 w-6 p-0">
              <X className="w-3 h-3" />
            </Button>
          </div>
        )}
        
        {renderFileTree(fileTree)}
      </div>
    </div>
  );
};

export default FileSystemManager;
