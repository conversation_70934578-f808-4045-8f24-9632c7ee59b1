import React, { useState, useEffect, useCallback } from 'react';
import { 
  <PERSON>ertTriangle, 
  CheckCircle, 
  Info, 
  Zap, 
  Shield, 
  TrendingUp,
  Code,
  FileText,
  Bug,
  Lightbulb
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { InvokeLLM } from '@/api/llm';

const CodeAnalyzer = ({
  fileStructure = {},
  activeFile,
  onSuggestionApply,
  className = ''
}) => {
  const [analysis, setAnalysis] = useState(null);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState('all');

  // Analyze code when file changes
  useEffect(() => {
    if (activeFile && fileStructure[activeFile]) {
      analyzeCode();
    }
  }, [activeFile, fileStructure]);

  // Perform code analysis
  const analyzeCode = useCallback(async () => {
    if (!activeFile || !fileStructure[activeFile]) return;

    setIsAnalyzing(true);
    try {
      const code = fileStructure[activeFile];
      const fileExtension = activeFile.split('.').pop();
      
      // Basic static analysis
      const basicAnalysis = performBasicAnalysis(code, fileExtension);
      
      // AI-powered analysis for complex issues
      const aiAnalysis = await performAIAnalysis(code, activeFile);
      
      setAnalysis({
        ...basicAnalysis,
        aiSuggestions: aiAnalysis,
        timestamp: new Date()
      });
    } catch (error) {
      console.error('Code analysis failed:', error);
    } finally {
      setIsAnalyzing(false);
    }
  }, [activeFile, fileStructure]);

  // Basic static analysis
  const performBasicAnalysis = useCallback((code, fileExtension) => {
    const lines = code.split('\n');
    const issues = [];
    const suggestions = [];
    let score = 100;

    // Check for common issues
    lines.forEach((line, index) => {
      const lineNum = index + 1;
      
      // Long lines
      if (line.length > 120) {
        issues.push({
          type: 'warning',
          category: 'style',
          line: lineNum,
          message: 'Line too long (>120 characters)',
          severity: 'low'
        });
        score -= 2;
      }

      // Console.log statements
      if (line.includes('console.log')) {
        issues.push({
          type: 'info',
          category: 'cleanup',
          line: lineNum,
          message: 'Remove console.log in production',
          severity: 'low'
        });
        score -= 1;
      }

      // TODO comments
      if (line.includes('TODO') || line.includes('FIXME')) {
        issues.push({
          type: 'info',
          category: 'todo',
          line: lineNum,
          message: 'Unfinished implementation',
          severity: 'medium'
        });
        score -= 3;
      }

      // Security issues
      if (line.includes('eval(') || line.includes('innerHTML')) {
        issues.push({
          type: 'error',
          category: 'security',
          line: lineNum,
          message: 'Potential security vulnerability',
          severity: 'high'
        });
        score -= 10;
      }
    });

    // Check for React-specific issues
    if (fileExtension === 'jsx' || fileExtension === 'tsx') {
      // Missing key props
      if (code.includes('.map(') && !code.includes('key=')) {
        issues.push({
          type: 'warning',
          category: 'react',
          message: 'Missing key prop in list rendering',
          severity: 'medium'
        });
        score -= 5;
      }

      // Inline styles
      if (code.includes('style={{')) {
        suggestions.push({
          type: 'optimization',
          category: 'performance',
          message: 'Consider using CSS classes instead of inline styles',
          impact: 'medium'
        });
      }
    }

    // Performance suggestions
    if (code.includes('useEffect') && !code.includes('[]')) {
      suggestions.push({
        type: 'optimization',
        category: 'performance',
        message: 'Consider adding dependency array to useEffect',
        impact: 'high'
      });
    }

    return {
      score: Math.max(0, score),
      issues,
      suggestions,
      metrics: {
        lines: lines.length,
        complexity: calculateComplexity(code),
        maintainability: calculateMaintainability(code)
      }
    };
  }, []);

  // AI-powered analysis
  const performAIAnalysis = useCallback(async (code, fileName) => {
    try {
      const prompt = `Analyze this ${fileName} code for:
1. Code quality and best practices
2. Performance optimizations
3. Security vulnerabilities
4. Maintainability improvements

Code:
\`\`\`
${code.slice(0, 2000)}
\`\`\`

Provide specific, actionable suggestions with line numbers when possible.`;

      const response = await InvokeLLM.invoke(prompt, { maxTokens: 800 });
      
      // Parse AI response into structured suggestions
      return parseAISuggestions(response);
    } catch (error) {
      console.error('AI analysis failed:', error);
      return [];
    }
  }, []);

  // Calculate code complexity
  const calculateComplexity = useCallback((code) => {
    const complexityIndicators = [
      /if\s*\(/g,
      /else/g,
      /for\s*\(/g,
      /while\s*\(/g,
      /switch\s*\(/g,
      /catch\s*\(/g,
      /\?\s*:/g // ternary operators
    ];

    let complexity = 1; // Base complexity
    complexityIndicators.forEach(pattern => {
      const matches = code.match(pattern);
      if (matches) {
        complexity += matches.length;
      }
    });

    return Math.min(complexity, 20); // Cap at 20
  }, []);

  // Calculate maintainability score
  const calculateMaintainability = useCallback((code) => {
    const lines = code.split('\n');
    const nonEmptyLines = lines.filter(line => line.trim().length > 0);
    const commentLines = lines.filter(line => line.trim().startsWith('//') || line.trim().startsWith('/*'));
    
    const commentRatio = commentLines.length / nonEmptyLines.length;
    const avgLineLength = nonEmptyLines.reduce((sum, line) => sum + line.length, 0) / nonEmptyLines.length;
    
    let score = 100;
    
    // Penalize low comment ratio
    if (commentRatio < 0.1) score -= 20;
    
    // Penalize very long average line length
    if (avgLineLength > 80) score -= 15;
    
    // Penalize very long functions (rough estimate)
    const functionMatches = code.match(/function\s+\w+|=>\s*{|\w+\s*\(/g);
    if (functionMatches && nonEmptyLines.length / functionMatches.length > 50) {
      score -= 25;
    }
    
    return Math.max(0, score);
  }, []);

  // Parse AI suggestions
  const parseAISuggestions = useCallback((response) => {
    // Simple parsing - in a real implementation, you'd use more sophisticated NLP
    const suggestions = [];
    const lines = response.split('\n');
    
    lines.forEach(line => {
      if (line.includes('suggestion') || line.includes('improve') || line.includes('consider')) {
        suggestions.push({
          type: 'ai',
          category: 'improvement',
          message: line.trim(),
          confidence: 'medium'
        });
      }
    });
    
    return suggestions;
  }, []);

  // Filter issues by category
  const getFilteredIssues = useCallback(() => {
    if (!analysis) return [];
    
    if (selectedCategory === 'all') {
      return analysis.issues;
    }
    
    return analysis.issues.filter(issue => issue.category === selectedCategory);
  }, [analysis, selectedCategory]);

  // Get issue icon
  const getIssueIcon = (type) => {
    switch (type) {
      case 'error':
        return <AlertTriangle className="w-4 h-4 text-red-500" />;
      case 'warning':
        return <AlertTriangle className="w-4 h-4 text-yellow-500" />;
      case 'info':
        return <Info className="w-4 h-4 text-blue-500" />;
      default:
        return <CheckCircle className="w-4 h-4 text-green-500" />;
    }
  };

  // Get score color
  const getScoreColor = (score) => {
    if (score >= 80) return 'text-green-500';
    if (score >= 60) return 'text-yellow-500';
    return 'text-red-500';
  };

  const filteredIssues = getFilteredIssues();
  const categories = analysis ? [...new Set(analysis.issues.map(issue => issue.category))] : [];

  return (
    <div className={`flex flex-col h-full bg-gray-900 text-white ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between p-3 bg-gray-800 border-b border-gray-700">
        <div className="flex items-center gap-2">
          <Code className="w-4 h-4 text-green-400" />
          <span className="text-sm font-medium">Code Analysis</span>
          {analysis && (
            <Badge variant="outline" className="text-xs">
              {activeFile}
            </Badge>
          )}
        </div>
        
        <Button
          size="sm"
          variant="ghost"
          onClick={analyzeCode}
          disabled={isAnalyzing || !activeFile}
          className="h-7 px-2"
        >
          {isAnalyzing ? (
            <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-blue-500" />
          ) : (
            <Zap className="w-3 h-3" />
          )}
        </Button>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-auto p-4">
        {!activeFile ? (
          <div className="text-center text-gray-500 mt-8">
            <Code className="w-16 h-16 mx-auto mb-4 opacity-50" />
            <p>Select a file to analyze</p>
          </div>
        ) : isAnalyzing ? (
          <div className="text-center text-gray-500 mt-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-4" />
            <p>Analyzing code...</p>
          </div>
        ) : analysis ? (
          <div className="space-y-4">
            {/* Score Overview */}
            <div className="bg-gray-800 rounded-lg p-4">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium">Code Quality Score</span>
                <span className={`text-2xl font-bold ${getScoreColor(analysis.score)}`}>
                  {analysis.score}/100
                </span>
              </div>
              <Progress value={analysis.score} className="h-2" />
              
              <div className="grid grid-cols-3 gap-4 mt-4 text-xs">
                <div className="text-center">
                  <div className="text-gray-400">Lines</div>
                  <div className="font-medium">{analysis.metrics.lines}</div>
                </div>
                <div className="text-center">
                  <div className="text-gray-400">Complexity</div>
                  <div className="font-medium">{analysis.metrics.complexity}</div>
                </div>
                <div className="text-center">
                  <div className="text-gray-400">Maintainability</div>
                  <div className="font-medium">{analysis.metrics.maintainability}%</div>
                </div>
              </div>
            </div>

            {/* Category Filter */}
            <div className="flex flex-wrap gap-2">
              <Button
                size="sm"
                variant={selectedCategory === 'all' ? 'default' : 'outline'}
                onClick={() => setSelectedCategory('all')}
                className="h-7 text-xs"
              >
                All ({analysis.issues.length})
              </Button>
              {categories.map(category => (
                <Button
                  key={category}
                  size="sm"
                  variant={selectedCategory === category ? 'default' : 'outline'}
                  onClick={() => setSelectedCategory(category)}
                  className="h-7 text-xs"
                >
                  {category} ({analysis.issues.filter(i => i.category === category).length})
                </Button>
              ))}
            </div>

            {/* Issues List */}
            <div className="space-y-2">
              {filteredIssues.map((issue, index) => (
                <div key={index} className="bg-gray-800 rounded-lg p-3">
                  <div className="flex items-start gap-3">
                    {getIssueIcon(issue.type)}
                    <div className="flex-1">
                      <div className="text-sm font-medium">{issue.message}</div>
                      {issue.line && (
                        <div className="text-xs text-gray-400 mt-1">
                          Line {issue.line}
                        </div>
                      )}
                    </div>
                    <Badge 
                      variant="outline" 
                      className={`text-xs ${
                        issue.severity === 'high' ? 'border-red-500 text-red-400' :
                        issue.severity === 'medium' ? 'border-yellow-500 text-yellow-400' :
                        'border-gray-500 text-gray-400'
                      }`}
                    >
                      {issue.severity}
                    </Badge>
                  </div>
                </div>
              ))}
            </div>

            {/* AI Suggestions */}
            {analysis.aiSuggestions && analysis.aiSuggestions.length > 0 && (
              <div className="bg-gray-800 rounded-lg p-4">
                <div className="flex items-center gap-2 mb-3">
                  <Lightbulb className="w-4 h-4 text-yellow-400" />
                  <span className="text-sm font-medium">AI Suggestions</span>
                </div>
                <div className="space-y-2">
                  {analysis.aiSuggestions.map((suggestion, index) => (
                    <div key={index} className="text-sm text-gray-300">
                      • {suggestion.message}
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        ) : (
          <div className="text-center text-gray-500 mt-8">
            <FileText className="w-16 h-16 mx-auto mb-4 opacity-50" />
            <p>Click analyze to check your code</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default CodeAnalyzer;
