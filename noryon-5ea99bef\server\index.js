import dotenv from 'dotenv';

// Load environment variables from parent directory FIRST
dotenv.config({ path: '../.env' });

import express from 'express';
import cors from 'cors';
import { spawn, exec } from 'child_process';
import fs from 'fs/promises';
import path from 'path';
import { fileURLToPath } from 'url';
import { InvokeLLM } from '../src/api/llm.js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const projectRoot = path.join(__dirname, '..');

const app = express();
const PORT = process.env.PORT || 4000;

app.use(cors());
app.use(express.json({ limit: '2mb' }));

// Store active processes
const activeProcesses = new Map();
let processCounter = 0;

// Health-check
app.get('/health', (_req, res) => res.json({ status: 'ok' }));

// Execute terminal command
app.post('/execute', async (req, res) => {
  try {
    const { command, cwd = projectRoot } = req.body;

    if (!command) {
      return res.status(400).json({ error: 'Command is required' });
    }

    console.log(`Executing command: ${command} in ${cwd}`);

    const result = await executeCommand(command, cwd);
    res.json(result);
  } catch (error) {
    console.error('Command execution error:', error);
    res.status(500).json({
      error: 'Command execution failed',
      message: error.message
    });
  }
});

// Start a development server
app.post('/dev-server/start', async (req, res) => {
  try {
    const { projectPath = projectRoot, port = 3000 } = req.body;

    const processId = await startDevServer(projectPath, port);
    res.json({
      success: true,
      processId,
      port,
      url: `http://localhost:${port}`
    });
  } catch (error) {
    console.error('Dev server start error:', error);
    res.status(500).json({
      error: 'Failed to start dev server',
      message: error.message
    });
  }
});

// Stop a development server
app.post('/dev-server/stop', async (req, res) => {
  try {
    const { processId } = req.body;

    const result = await stopProcess(processId);
    res.json(result);
  } catch (error) {
    console.error('Dev server stop error:', error);
    res.status(500).json({
      error: 'Failed to stop dev server',
      message: error.message
    });
  }
});

// File system operations
app.post('/fs/read', async (req, res) => {
  try {
    const { filePath } = req.body;
    const fullPath = path.resolve(projectRoot, filePath);

    // Security check - ensure path is within project
    if (!fullPath.startsWith(projectRoot)) {
      return res.status(403).json({ error: 'Access denied' });
    }

    const content = await fs.readFile(fullPath, 'utf8');
    res.json({ content });
  } catch (error) {
    res.status(404).json({ error: 'File not found', message: error.message });
  }
});

app.post('/fs/write', async (req, res) => {
  try {
    const { filePath, content } = req.body;
    const fullPath = path.resolve(projectRoot, filePath);

    // Security check
    if (!fullPath.startsWith(projectRoot)) {
      return res.status(403).json({ error: 'Access denied' });
    }

    // Ensure directory exists
    await fs.mkdir(path.dirname(fullPath), { recursive: true });
    await fs.writeFile(fullPath, content, 'utf8');

    res.json({ success: true });
  } catch (error) {
    res.status(500).json({ error: 'Write failed', message: error.message });
  }
});

app.post('/fs/delete', async (req, res) => {
  try {
    const { filePath } = req.body;
    const fullPath = path.resolve(projectRoot, filePath);

    // Security check
    if (!fullPath.startsWith(projectRoot)) {
      return res.status(403).json({ error: 'Access denied' });
    }

    await fs.unlink(fullPath);
    res.json({ success: true });
  } catch (error) {
    res.status(500).json({ error: 'Delete failed', message: error.message });
  }
});

app.post('/fs/list', async (req, res) => {
  try {
    const { dirPath = '.' } = req.body;
    const fullPath = path.resolve(projectRoot, dirPath);

    // Security check
    if (!fullPath.startsWith(projectRoot)) {
      return res.status(403).json({ error: 'Access denied' });
    }

    const items = await fs.readdir(fullPath, { withFileTypes: true });
    const result = items.map(item => ({
      name: item.name,
      type: item.isDirectory() ? 'directory' : 'file',
      path: path.join(dirPath, item.name)
    }));

    res.json({ items: result });
  } catch (error) {
    res.status(500).json({ error: 'List failed', message: error.message });
  }
});

// POST /generate/web  { prompt: string }
// Returns a basic diff list now; later we will upgrade to streaming chunks.
app.post('/generate/web', async (req, res) => {
  const { prompt } = req.body || {};
  if (typeof prompt !== 'string' || !prompt.trim()) {
    return res.status(400).json({ error: 'prompt (string) required' });
  }

  try {
    const systemInstruction = `You are a senior React developer generating production-ready Vite+React code only for web. Avoid mobile frameworks.`;

    // Simple invocation for MVP – no streaming yet
    const llmPrompt = `${systemInstruction}\n\nUser request: ${prompt}\n\nReturn ONLY the JSON file tree with real code.`;
    const llmResponse = await InvokeLLM.invoke(llmPrompt, { maxTokens: 4000 });

    res.json({ files: llmResponse });
  } catch (err) {
    console.error('LLM generation failed:', err);
    res.status(500).json({ error: 'code_generation_failed', message: err.message });
  }
});

// Command execution functions
async function executeCommand(command, cwd) {
  return new Promise((resolve, reject) => {
    const isWindows = process.platform === 'win32';
    const shell = isWindows ? 'powershell.exe' : '/bin/bash';
    const shellArgs = isWindows ? ['-Command'] : ['-c'];

    const child = spawn(shell, [...shellArgs, command], {
      cwd,
      stdio: ['pipe', 'pipe', 'pipe'],
      shell: false
    });

    let stdout = '';
    let stderr = '';

    child.stdout.on('data', (data) => {
      stdout += data.toString();
    });

    child.stderr.on('data', (data) => {
      stderr += data.toString();
    });

    child.on('close', (code) => {
      resolve({
        success: code === 0,
        code,
        stdout: stdout.trim(),
        stderr: stderr.trim(),
        output: stdout.trim() || stderr.trim()
      });
    });

    child.on('error', (error) => {
      reject(error);
    });

    // Timeout after 30 seconds
    setTimeout(() => {
      child.kill();
      reject(new Error('Command timeout'));
    }, 30000);
  });
}

async function startDevServer(projectPath, port) {
  return new Promise((resolve, reject) => {
    const processId = ++processCounter;

    // Check if package.json exists and has dev script
    const packageJsonPath = path.join(projectPath, 'package.json');

    fs.readFile(packageJsonPath, 'utf8')
      .then(data => {
        const packageJson = JSON.parse(data);
        const devScript = packageJson.scripts?.dev || packageJson.scripts?.start;

        if (!devScript) {
          throw new Error('No dev or start script found in package.json');
        }

        const command = devScript.includes('vite') ? 'npm run dev' :
                      devScript.includes('webpack') ? 'npm run dev' :
                      devScript.includes('next') ? 'npm run dev' :
                      'npm run dev';

        const child = spawn('npm', ['run', 'dev'], {
          cwd: projectPath,
          stdio: ['pipe', 'pipe', 'pipe'],
          shell: true,
          env: { ...process.env, PORT: port.toString() }
        });

        activeProcesses.set(processId, {
          process: child,
          type: 'dev-server',
          port,
          startTime: new Date()
        });

        let output = '';
        child.stdout.on('data', (data) => {
          output += data.toString();
          // Check if server is ready
          if (output.includes('Local:') || output.includes('localhost') || output.includes('ready')) {
            resolve(processId);
          }
        });

        child.stderr.on('data', (data) => {
          output += data.toString();
        });

        child.on('error', (error) => {
          activeProcesses.delete(processId);
          reject(error);
        });

        child.on('exit', (code) => {
          activeProcesses.delete(processId);
          if (code !== 0) {
            reject(new Error(`Dev server exited with code ${code}`));
          }
        });

        // Timeout if server doesn't start in 30 seconds
        setTimeout(() => {
          if (activeProcesses.has(processId)) {
            resolve(processId); // Assume it started even if we didn't see the ready message
          }
        }, 30000);
      })
      .catch(reject);
  });
}

async function stopProcess(processId) {
  const processInfo = activeProcesses.get(processId);

  if (!processInfo) {
    return { success: false, error: 'Process not found' };
  }

  try {
    processInfo.process.kill();
    activeProcesses.delete(processId);
    return { success: true };
  } catch (error) {
    return { success: false, error: error.message };
  }
}

app.listen(PORT, () => {
  console.log(`Backend running on http://localhost:${PORT}`);
});