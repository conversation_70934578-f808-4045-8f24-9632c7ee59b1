// Backend service for real command execution and file operations
const API_BASE = 'http://localhost:4000';

class BackendService {
  // Execute terminal command
  static async executeCommand(command, cwd = '.') {
    try {
      const response = await fetch(`${API_BASE}/execute`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ command, cwd }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Command execution failed:', error);
      throw error;
    }
  }

  // File system operations
  static async readFile(filePath) {
    try {
      const response = await fetch(`${API_BASE}/fs/read`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ filePath }),
      });

      if (!response.ok) {
        throw new Error(`Failed to read file: ${response.status}`);
      }

      const result = await response.json();
      return result.content;
    } catch (error) {
      console.error('File read failed:', error);
      throw error;
    }
  }

  static async writeFile(filePath, content) {
    try {
      const response = await fetch(`${API_BASE}/fs/write`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ filePath, content }),
      });

      if (!response.ok) {
        throw new Error(`Failed to write file: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('File write failed:', error);
      throw error;
    }
  }

  static async deleteFile(filePath) {
    try {
      const response = await fetch(`${API_BASE}/fs/delete`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ filePath }),
      });

      if (!response.ok) {
        throw new Error(`Failed to delete file: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('File delete failed:', error);
      throw error;
    }
  }

  static async listDirectory(dirPath = '.') {
    try {
      const response = await fetch(`${API_BASE}/fs/list`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ dirPath }),
      });

      if (!response.ok) {
        throw new Error(`Failed to list directory: ${response.status}`);
      }

      const result = await response.json();
      return result.items;
    } catch (error) {
      console.error('Directory list failed:', error);
      throw error;
    }
  }

  // Development server operations
  static async startDevServer(projectPath = '.', port = 3000) {
    try {
      const response = await fetch(`${API_BASE}/dev-server/start`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ projectPath, port }),
      });

      if (!response.ok) {
        throw new Error(`Failed to start dev server: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Dev server start failed:', error);
      throw error;
    }
  }

  static async stopDevServer(processId) {
    try {
      const response = await fetch(`${API_BASE}/dev-server/stop`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ processId }),
      });

      if (!response.ok) {
        throw new Error(`Failed to stop dev server: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Dev server stop failed:', error);
      throw error;
    }
  }

  // Specialized command helpers
  static async npmInstall(packageName = '', cwd = '.') {
    const command = packageName ? `npm install ${packageName}` : 'npm install';
    return await this.executeCommand(command, cwd);
  }

  static async npmRunScript(scriptName, cwd = '.') {
    return await this.executeCommand(`npm run ${scriptName}`, cwd);
  }

  static async gitCommand(gitArgs, cwd = '.') {
    return await this.executeCommand(`git ${gitArgs}`, cwd);
  }

  static async createProject(projectName, template = 'vite') {
    const commands = {
      vite: `npm create vite@latest ${projectName} -- --template react`,
      nextjs: `npx create-next-app@latest ${projectName}`,
      cra: `npx create-react-app ${projectName}`,
    };

    const command = commands[template] || commands.vite;
    return await this.executeCommand(command);
  }

  // AI-powered command suggestions
  static async getCommandSuggestions(context, userInput) {
    try {
      // This would integrate with your AI service
      const suggestions = [];
      
      // Basic command suggestions based on context
      if (userInput.includes('install')) {
        suggestions.push('npm install', 'npm install --save-dev', 'yarn install');
      }
      
      if (userInput.includes('git')) {
        suggestions.push('git status', 'git add .', 'git commit -m ""', 'git push');
      }
      
      if (userInput.includes('build')) {
        suggestions.push('npm run build', 'npm run dev', 'npm run start');
      }

      return suggestions;
    } catch (error) {
      console.error('Command suggestions failed:', error);
      return [];
    }
  }

  // Project structure analysis
  static async analyzeProject(projectPath = '.') {
    try {
      const items = await this.listDirectory(projectPath);
      const analysis = {
        hasPackageJson: false,
        hasNodeModules: false,
        hasGitRepo: false,
        framework: 'unknown',
        buildTool: 'unknown',
        dependencies: []
      };

      // Check for key files
      for (const item of items) {
        if (item.name === 'package.json') {
          analysis.hasPackageJson = true;
          try {
            const packageContent = await this.readFile('package.json');
            const packageJson = JSON.parse(packageContent);
            analysis.dependencies = Object.keys(packageJson.dependencies || {});
            
            // Detect framework
            if (packageJson.dependencies?.react) analysis.framework = 'react';
            if (packageJson.dependencies?.vue) analysis.framework = 'vue';
            if (packageJson.dependencies?.angular) analysis.framework = 'angular';
            if (packageJson.dependencies?.next) analysis.framework = 'nextjs';
            
            // Detect build tool
            if (packageJson.devDependencies?.vite) analysis.buildTool = 'vite';
            if (packageJson.devDependencies?.webpack) analysis.buildTool = 'webpack';
            if (packageJson.scripts?.dev?.includes('vite')) analysis.buildTool = 'vite';
          } catch (e) {
            console.warn('Could not parse package.json');
          }
        }
        
        if (item.name === 'node_modules') analysis.hasNodeModules = true;
        if (item.name === '.git') analysis.hasGitRepo = true;
      }

      return analysis;
    } catch (error) {
      console.error('Project analysis failed:', error);
      return null;
    }
  }

  // Health check
  static async healthCheck() {
    try {
      const response = await fetch(`${API_BASE}/health`);
      return response.ok;
    } catch (error) {
      return false;
    }
  }
}

export default BackendService;
