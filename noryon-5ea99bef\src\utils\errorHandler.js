// Comprehensive error handling and logging system
class ErrorHandler {
  static logError(error, context = '') {
    const timestamp = new Date().toISOString();
    const errorInfo = {
      timestamp,
      context,
      message: error.message,
      stack: error.stack,
      type: error.constructor.name
    };
    
    console.error(`[${timestamp}] ${context}:`, errorInfo);
    
    // Store in localStorage for debugging
    try {
      const errors = JSON.parse(localStorage.getItem('ide_errors') || '[]');
      errors.push(errorInfo);
      
      // Keep only last 50 errors
      if (errors.length > 50) {
        errors.splice(0, errors.length - 50);
      }
      
      localStorage.setItem('ide_errors', JSON.stringify(errors));
    } catch (e) {
      console.warn('Failed to store error in localStorage:', e);
    }
    
    return errorInfo;
  }

  static getStoredErrors() {
    try {
      return JSON.parse(localStorage.getItem('ide_errors') || '[]');
    } catch (e) {
      return [];
    }
  }

  static clearStoredErrors() {
    localStorage.removeItem('ide_errors');
  }

  static handleAsyncError(promise, context = '') {
    return promise.catch(error => {
      this.logError(error, context);
      throw error;
    });
  }

  static createErrorBoundary(component, fallback) {
    return class ErrorBoundary extends React.Component {
      constructor(props) {
        super(props);
        this.state = { hasError: false, error: null };
      }

      static getDerivedStateFromError(error) {
        return { hasError: true, error };
      }

      componentDidCatch(error, errorInfo) {
        ErrorHandler.logError(error, `ErrorBoundary: ${component.name}`);
      }

      render() {
        if (this.state.hasError) {
          return fallback ? fallback(this.state.error) : (
            <div className="p-4 bg-red-900 text-red-100 rounded-lg">
              <h3 className="font-bold">Something went wrong</h3>
              <p className="text-sm mt-2">{this.state.error?.message}</p>
            </div>
          );
        }

        return React.createElement(component, this.props);
      }
    };
  }
}

// Process management utilities
class ProcessManager {
  constructor() {
    this.processes = new Map();
    this.eventListeners = new Map();
  }

  // Register a process
  registerProcess(id, type, metadata = {}) {
    const process = {
      id,
      type,
      status: 'running',
      startTime: new Date(),
      metadata,
      lastActivity: new Date()
    };
    
    this.processes.set(id, process);
    this.emit('processStarted', process);
    
    return process;
  }

  // Update process status
  updateProcess(id, updates) {
    const process = this.processes.get(id);
    if (process) {
      Object.assign(process, updates, { lastActivity: new Date() });
      this.emit('processUpdated', process);
    }
  }

  // Remove process
  removeProcess(id) {
    const process = this.processes.get(id);
    if (process) {
      this.processes.delete(id);
      this.emit('processStopped', process);
    }
  }

  // Get all processes
  getAllProcesses() {
    return Array.from(this.processes.values());
  }

  // Get processes by type
  getProcessesByType(type) {
    return this.getAllProcesses().filter(p => p.type === type);
  }

  // Clean up stale processes
  cleanupStaleProcesses(maxAge = 5 * 60 * 1000) { // 5 minutes
    const now = new Date();
    const staleProcesses = [];
    
    for (const [id, process] of this.processes) {
      if (now - process.lastActivity > maxAge) {
        staleProcesses.push(id);
      }
    }
    
    staleProcesses.forEach(id => this.removeProcess(id));
    
    return staleProcesses.length;
  }

  // Event system
  on(event, callback) {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, []);
    }
    this.eventListeners.get(event).push(callback);
  }

  off(event, callback) {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      const index = listeners.indexOf(callback);
      if (index > -1) {
        listeners.splice(index, 1);
      }
    }
  }

  emit(event, data) {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      listeners.forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          ErrorHandler.logError(error, `ProcessManager event: ${event}`);
        }
      });
    }
  }
}

// Retry utility for failed operations
class RetryManager {
  static async retry(operation, options = {}) {
    const {
      maxAttempts = 3,
      delay = 1000,
      backoff = 2,
      shouldRetry = () => true
    } = options;

    let lastError;
    
    for (let attempt = 1; attempt <= maxAttempts; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = error;
        
        if (attempt === maxAttempts || !shouldRetry(error, attempt)) {
          throw error;
        }
        
        const waitTime = delay * Math.pow(backoff, attempt - 1);
        await new Promise(resolve => setTimeout(resolve, waitTime));
      }
    }
    
    throw lastError;
  }
}

// Performance monitoring
class PerformanceMonitor {
  constructor() {
    this.metrics = new Map();
  }

  startTimer(name) {
    this.metrics.set(name, { startTime: performance.now() });
  }

  endTimer(name) {
    const metric = this.metrics.get(name);
    if (metric) {
      metric.duration = performance.now() - metric.startTime;
      metric.endTime = performance.now();
      return metric.duration;
    }
    return null;
  }

  getMetrics() {
    return Object.fromEntries(this.metrics);
  }

  clearMetrics() {
    this.metrics.clear();
  }

  // Monitor memory usage
  getMemoryUsage() {
    if (performance.memory) {
      return {
        used: performance.memory.usedJSHeapSize,
        total: performance.memory.totalJSHeapSize,
        limit: performance.memory.jsHeapSizeLimit
      };
    }
    return null;
  }
}

// Health check system
class HealthChecker {
  constructor() {
    this.checks = new Map();
    this.results = new Map();
  }

  addCheck(name, checkFunction, interval = 30000) {
    this.checks.set(name, {
      function: checkFunction,
      interval,
      lastRun: null,
      intervalId: null
    });
    
    this.startCheck(name);
  }

  startCheck(name) {
    const check = this.checks.get(name);
    if (check && !check.intervalId) {
      // Run immediately
      this.runCheck(name);
      
      // Schedule periodic runs
      check.intervalId = setInterval(() => {
        this.runCheck(name);
      }, check.interval);
    }
  }

  stopCheck(name) {
    const check = this.checks.get(name);
    if (check && check.intervalId) {
      clearInterval(check.intervalId);
      check.intervalId = null;
    }
  }

  async runCheck(name) {
    const check = this.checks.get(name);
    if (!check) return;

    try {
      const result = await check.function();
      this.results.set(name, {
        status: 'healthy',
        result,
        timestamp: new Date(),
        error: null
      });
    } catch (error) {
      this.results.set(name, {
        status: 'unhealthy',
        result: null,
        timestamp: new Date(),
        error: error.message
      });
      
      ErrorHandler.logError(error, `Health check: ${name}`);
    }
    
    check.lastRun = new Date();
  }

  getResults() {
    return Object.fromEntries(this.results);
  }

  isHealthy() {
    const results = Array.from(this.results.values());
    return results.length > 0 && results.every(r => r.status === 'healthy');
  }
}

// Global instances
const processManager = new ProcessManager();
const performanceMonitor = new PerformanceMonitor();
const healthChecker = new HealthChecker();

export {
  ErrorHandler,
  ProcessManager,
  RetryManager,
  PerformanceMonitor,
  HealthChecker,
  processManager,
  performanceMonitor,
  healthChecker
};
